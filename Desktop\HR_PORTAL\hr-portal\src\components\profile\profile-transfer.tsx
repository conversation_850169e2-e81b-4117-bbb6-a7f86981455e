import React, { useState, useEffect } from "react";
import { AnimatedTable } from "../ui/animated-table";
import { Button } from "../ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { Select } from "../ui/select";
import { Label } from "../ui/label";

import { motion } from "framer-motion";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { fetchActiveUsers } from "@/store/slices/activeUsersSlice";
import { selectActiveUsers, selectActiveUsersLoading } from "@/store/selectors/activeUsersSelectors";

// Define types
interface Recruiter {
  id: string;
  name: string;
  email: string;
  activeProfiles: number;
}

interface CandidateProfile {
  id: string;
  name: string;
  currentRecruiter: string;
  recruiterId: string;
  jobTitle: string;
  status:
    | "New"
    | "Contacted"
    | "Interviewing"
    | "Offered"
    | "Hired"
    | "Rejected";
  lastUpdated: string;
}

export function ProfileTransfer() {
  const dispatch = useAppDispatch();
  const activeUsers = useAppSelector(selectActiveUsers);
  const usersLoading = useAppSelector(selectActiveUsersLoading);

  const [profiles, setProfiles] = useState<CandidateProfile[]>([]);
  const [selectedProfiles, setSelectedProfiles] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isTransferDialogOpen, setIsTransferDialogOpen] = useState(false);
  const [targetRecruiter, setTargetRecruiter] = useState("");
  const [transferSuccess, setTransferSuccess] = useState(false);

  // Convert active users to recruiters format
  const recruiters: Recruiter[] = activeUsers
    .filter(user => user.userType === 'recruiter' && user.isActive)
    .map(user => ({
      id: user.id.toString(),
      name: `${user.firstName} ${user.lastName}`.trim(),
      email: user.email,
      activeProfiles: Math.floor(Math.random() * 15) + 1, // Mock active profiles count for now
    }));

  // Fetch active users and load profile data
  useEffect(() => {
    const loadData = async () => {
      // Fetch active users from Redux
      dispatch(fetchActiveUsers());

      // Simulate loading time for profiles
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Mock candidate profiles data - will be updated to use real recruiters when available
      const mockProfiles = [
        {
          id: "c1",
          name: "Chris Wilson",
          jobTitle: "Senior Developer",
          status: "Interviewing" as const,
          lastUpdated: "2025-04-28",
        },
        {
          id: "c2",
          name: "Priya Sharma",
          jobTitle: "UX Designer",
          status: "New" as const,
          lastUpdated: "2025-04-30",
        },
        {
          id: "c3",
          name: "Thomas Lee",
          jobTitle: "Product Manager",
          status: "Contacted" as const,
          lastUpdated: "2025-04-29",
        },
        {
          id: "c4",
          name: "Elena Rodriguez",
          jobTitle: "Data Scientist",
          status: "Offered" as const,
          lastUpdated: "2025-04-25",
        },
        {
          id: "c5",
          name: "David Kim",
          jobTitle: "DevOps Engineer",
          status: "Hired" as const,
          lastUpdated: "2025-04-15",
        },
      ];

      setProfiles(mockProfiles.map((profile) => ({
        ...profile,
        currentRecruiter: "Unassigned", // Will be updated when recruiters are loaded
        recruiterId: "",
      })));

      setIsLoading(false);
    };

    loadData();
  }, [dispatch]);

  // Update profiles with real recruiter assignments when recruiters are loaded
  useEffect(() => {
    if (recruiters.length > 0 && profiles.length > 0) {
      setProfiles(prevProfiles =>
        prevProfiles.map((profile, index) => {
          // Assign recruiters in a round-robin fashion for demo purposes
          const recruiterIndex = index % recruiters.length;
          const assignedRecruiter = recruiters[recruiterIndex];
          return {
            ...profile,
            currentRecruiter: assignedRecruiter.name,
            recruiterId: assignedRecruiter.id,
          };
        })
      );
    }
  }, [recruiters, profiles.length]);

  // Handle profile selection
  const toggleProfileSelection = (profileId: string) => {
    setSelectedProfiles((prevSelected) =>
      prevSelected.includes(profileId)
        ? prevSelected.filter((id) => id !== profileId)
        : [...prevSelected, profileId]
    );
  };

  // Handle profile transfer
  const handleTransfer = async () => {
    if (!targetRecruiter || selectedProfiles.length === 0) return;

    // In a real application, this would be an API call
    setIsLoading(true);
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // Update the profiles with the new recruiter
    const targetRecruiterObj = recruiters.find((r) => r.id === targetRecruiter);
    if (!targetRecruiterObj) return;

    setProfiles((prevProfiles) =>
      prevProfiles.map((profile) =>
        selectedProfiles.includes(profile.id)
          ? {
              ...profile,
              currentRecruiter: targetRecruiterObj.name,
              recruiterId: targetRecruiterObj.id,
              lastUpdated: new Date().toISOString().split("T")[0],
            }
          : profile
      )
    );

    // Note: Recruiter counts are now computed from Redux state
    // In a real application, you would update the backend and refresh the data

    setSelectedProfiles([]);
    setIsLoading(false);
    setTransferSuccess(true);

    // Reset success message after 3 seconds
    setTimeout(() => {
      setTransferSuccess(false);
      setIsTransferDialogOpen(false);
    }, 3000);
  };

  const getStatusColor = (status: CandidateProfile["status"]) => {
    switch (status) {
      case "New":
        return "bg-blue-100 text-blue-800";
      case "Contacted":
        return "bg-purple-100 text-purple-800";
      case "Interviewing":
        return "bg-amber-100 text-amber-800";
      case "Offered":
        return "bg-emerald-100 text-emerald-800";
      case "Hired":
        return "bg-green-100 text-green-800";
      case "Rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="h-full w-full flex flex-col">
      <div className="bg-white p-6 w-full flex-1">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Profile Transfer
            </h1>
            <p className="text-gray-600">
              Transfer candidate profiles between recruiters and manage
              assignments
            </p>
          </div>

          <Button
            disabled={selectedProfiles.length === 0}
            onClick={() => setIsTransferDialogOpen(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Transfer Selected ({selectedProfiles.length})
          </Button>
        </div>

        <div className="flex flex-col space-y-8">
          {/* Recruiters Overview */}
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">
              Recruiters
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {recruiters.map((recruiter) => (
                <motion.div
                  key={recruiter.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="bg-gray-50 rounded-lg border border-gray-200 p-4"
                >
                  <h3 className="font-medium text-gray-900">
                    {recruiter.name}
                  </h3>
                  <p className="text-gray-500 text-sm">{recruiter.email}</p>
                  <div className="mt-2 flex items-center">
                    <span className="text-sm font-medium text-gray-900">
                      {recruiter.activeProfiles}
                    </span>
                    <span className="ml-1 text-sm text-gray-500">
                      active profiles
                    </span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Profiles Table */}
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">
              Candidate Profiles
            </h2>
            <AnimatedTable
              isLoading={isLoading}
              headers={
                <tr>
                  <th className="p-4">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        checked={
                          selectedProfiles.length > 0 &&
                          selectedProfiles.length === profiles.length
                        }
                        onChange={() => {
                          if (selectedProfiles.length === profiles.length) {
                            setSelectedProfiles([]);
                          } else {
                            setSelectedProfiles(profiles.map((p) => p.id));
                          }
                        }}
                      />
                    </div>
                  </th>
                  <th className="p-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Candidate
                  </th>
                  <th className="p-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Job Title
                  </th>
                  <th className="p-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Current Recruiter
                  </th>
                  <th className="p-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="p-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Updated
                  </th>
                </tr>
              }
            >
              {profiles.map((profile) => (
                <motion.tr
                  key={profile.id}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 10 }}
                  whileHover={{ backgroundColor: "rgba(0,0,0,0.02)" }}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => toggleProfileSelection(profile.id)}
                >
                  <td className="p-4">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        checked={selectedProfiles.includes(profile.id)}
                        onChange={() => toggleProfileSelection(profile.id)}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="font-medium text-gray-900">
                      {profile.name}
                    </div>
                  </td>
                  <td className="p-4 text-gray-500">{profile.jobTitle}</td>
                  <td className="p-4 text-gray-500">
                    {profile.currentRecruiter}
                  </td>
                  <td className="p-4">
                    <span
                      className={`px-2 py-1 text-xs rounded-full ${getStatusColor(
                        profile.status
                      )}`}
                    >
                      {profile.status}
                    </span>
                  </td>
                  <td className="p-4 text-gray-500">{profile.lastUpdated}</td>
                </motion.tr>
              ))}
            </AnimatedTable>
          </div>
        </div>
      </div>

      {/* Transfer Dialog */}
      <Dialog
        open={isTransferDialogOpen}
        onOpenChange={(open) => {
          if (!isLoading || !open) setIsTransferDialogOpen(open);
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Transfer Profiles</DialogTitle>
          </DialogHeader>
          {transferSuccess ? (
            <div className="p-4 text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <svg
                  className="h-6 w-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="mt-2 text-lg font-medium text-gray-900">
                Transfer Successful
              </h3>
              <p className="mt-1 text-gray-500">
                {selectedProfiles.length} profile(s) have been transferred
                successfully.
              </p>
            </div>
          ) : (
            <>
              <div className="p-4">
                <div className="mb-4">
                  <p className="text-sm text-gray-500 mb-4">
                    You are about to transfer {selectedProfiles.length}{" "}
                    candidate profile(s) to another recruiter. This action will
                    remove the profiles from their current recruiters.
                  </p>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="targetRecruiter">
                        Select Target Recruiter
                      </Label>
                      <Select
                        value={targetRecruiter}
                        onValueChange={setTargetRecruiter}
                        disabled={isLoading}
                      >
                        <option value="" disabled>
                          Select a recruiter
                        </option>
                        {recruiters.map((recruiter) => (
                          <option key={recruiter.id} value={recruiter.id}>
                            {recruiter.name} ({recruiter.activeProfiles}{" "}
                            profiles)
                          </option>
                        ))}
                      </Select>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-end bg-gray-50 p-4 rounded-b-lg space-x-3">
                <Button
                  variant="outline"
                  onClick={() => setIsTransferDialogOpen(false)}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  className="bg-blue-600 hover:bg-blue-700"
                  onClick={handleTransfer}
                  disabled={!targetRecruiter || isLoading}
                >
                  {isLoading ? "Transferring..." : "Confirm Transfer"}
                </Button>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
