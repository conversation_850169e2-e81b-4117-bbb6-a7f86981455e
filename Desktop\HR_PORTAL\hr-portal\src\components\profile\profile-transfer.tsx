import React, { useState, useEffect } from "react";
import { AnimatedTable } from "../ui/animated-table";
import { useUser } from "@/contexts/user-context";
import { motion } from "framer-motion";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "../ui/select";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { ApiService, type ActiveUser } from "@/services/api";

// Define types
interface Recruiter {
  id: string;
  name: string;
  email: string;
  activeProfiles: number;
}

interface CandidateProfile {
  id: string;
  name: string;
  currentRecruiter: string;
  recruiterId: string;
  jobTitle: string;
  status:
    | "New"
    | "Contacted"
    | "Interviewing"
    | "Offered"
    | "Hired"
    | "Rejected";
  lastUpdated: string;
}

export function ProfileTransfer() {
  const { userName } = useUser();
  const [recruiters, setRecruiters] = useState<Recruiter[]>([]);
  const [profiles, setProfiles] = useState<CandidateProfile[]>([]); // No demo data
  const [selectedProfiles, setSelectedProfiles] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedRecruiter, setSelectedRecruiter] = useState<Recruiter | null>(null);
  const [targetRecruiter, setTargetRecruiter] = useState<Recruiter | null>(null);
  const [recruiterSearch, setRecruiterSearch] = useState("");
  const [targetRecruiterSearch, setTargetRecruiterSearch] = useState("");
  const [transferSuccess, setTransferSuccess] = useState(false);

  // Fetch recruiters from user-accounts API (already implemented)
  useEffect(() => {
    const fetchRecruiters = async () => {
      setIsLoading(true);
      try {
        const response = await ApiService.fetchActiveUsers();
        const allActiveUsers = [
          ...response.active_users_manager,
          ...response.active_users_recruiter,
        ];
        const recruiterList = allActiveUsers
          .filter((u) => u.user_type === "recruiter")
          .map((u) => ({
            id: String(u.id),
            name: u.name,
            email: u.email,
            activeProfiles: 0, // You can update this if you have the count
          }));
        setRecruiters(recruiterList);
      } finally {
        setIsLoading(false);
      }
    };
    fetchRecruiters();
  }, []);

  // Remove demo/mock candidate profiles. Ready for real API integration.
  // useEffect(() => {
  //   // ...
  // }, []);

  // Filter profiles for selected recruiter
  const filteredProfiles = selectedRecruiter
    ? profiles.filter((p) => p.recruiterId === selectedRecruiter.id)
    : [];

  // Filter recruiters for dropdowns
  const filteredRecruiters = recruiters.filter((r) =>
    r.name.toLowerCase().includes(recruiterSearch.toLowerCase())
  );
  const filteredTargetRecruiters = recruiters.filter(
    (r) =>
      r.id !== selectedRecruiter?.id &&
      r.name.toLowerCase().includes(targetRecruiterSearch.toLowerCase())
  );

  // Handle profile selection
  const toggleProfileSelection = (profileId: string) => {
    setSelectedProfiles((prevSelected) =>
      prevSelected.includes(profileId)
        ? prevSelected.filter((id) => id !== profileId)
        : [...prevSelected, profileId]
    );
  };

  // Handle profile transfer
  const handleTransfer = async () => {
    if (!targetRecruiter || selectedProfiles.length === 0) return;

    // In a real application, this would be an API call
    setIsLoading(true);
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // Update the profiles with the new recruiter
    const targetRecruiterObj = recruiters.find((r) => r.id === targetRecruiter.id);
    if (!targetRecruiterObj) return;

    setProfiles((prevProfiles) =>
      prevProfiles.map((profile) =>
        selectedProfiles.includes(profile.id)
          ? {
              ...profile,
              currentRecruiter: targetRecruiterObj.name,
              recruiterId: targetRecruiterObj.id,
              lastUpdated: new Date().toISOString().split("T")[0],
            }
          : profile
      )
    );

    // Update recruiter counts (simplified)
    setRecruiters((prevRecruiters) =>
      prevRecruiters.map((recruiter) => ({
        ...recruiter,
        activeProfiles:
          recruiter.id === targetRecruiter.id
            ? recruiter.activeProfiles + selectedProfiles.length
            : recruiter.activeProfiles -
              profiles.filter(
                (p) =>
                  selectedProfiles.includes(p.id) &&
                  p.recruiterId === recruiter.id
              ).length,
      }))
    );

    setSelectedProfiles([]);
    setIsLoading(false);
    setTransferSuccess(true);

    // Reset success message after 3 seconds
    setTimeout(() => {
      setTransferSuccess(false);
    }, 3000);
  };

  const getStatusColor = (status: CandidateProfile["status"]) => {
    switch (status) {
      case "New":
        return "bg-blue-100 text-blue-800";
      case "Contacted":
        return "bg-purple-100 text-purple-800";
      case "Interviewing":
        return "bg-amber-100 text-amber-800";
      case "Offered":
        return "bg-emerald-100 text-emerald-800";
      case "Hired":
        return "bg-green-100 text-green-800";
      case "Rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="h-full w-full flex flex-col">
      <div className="bg-white p-6 w-full flex-1">
        {/* <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Profile Transfer
            </h1>
            <p className="text-gray-600">
              Transfer candidate profiles between recruiters and manage
              assignments
            </p>
          </div>

          <Button
            disabled={selectedProfiles.length === 0}
            onClick={() => setIsTransferDialogOpen(true)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Transfer Selected ({selectedProfiles.length})
          </Button>
        </div> */}

        <div className="flex flex-col space-y-8">
          {/* Recruiter Selection */}
          <div className="mb-6 flex flex-col sm:flex-row sm:items-center gap-4">
            <label className="font-semibold text-lg">Select Recruiter:</label>
            <div className="w-72">
              <Input
                placeholder="Search recruiter..."
                value={recruiterSearch}
                onChange={(e) => setRecruiterSearch(e.target.value)}
                className="mb-2"
              />
              <Select
                value={selectedRecruiter?.id || ""}
                onValueChange={(id) => {
                  const rec = recruiters.find((r) => r.id === id);
                  setSelectedRecruiter(rec || null);
                  setSelectedProfiles([]);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select recruiter" />
                </SelectTrigger>
                <SelectContent>
                  {filteredRecruiters.map((r) => (
                    <SelectItem key={r.id} value={r.id}>{r.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {selectedRecruiter && (
              <span className="ml-4 text-green-700 font-bold">
                Selected recruiter/manager: {selectedRecruiter.name}
              </span>
            )}
          </div>

          {/* Candidate Table */}
          <div className="mb-6">
            <label className="font-semibold text-lg">Select Candidates:</label>
            <div className="overflow-x-auto mt-2 rounded-lg border border-gray-200">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="p-2">
                      <input
                        type="checkbox"
                        checked={
                          selectedProfiles.length > 0 &&
                          selectedProfiles.length === filteredProfiles.length
                        }
                        onChange={() => {
                          if (selectedProfiles.length === filteredProfiles.length) {
                            setSelectedProfiles([]);
                          } else {
                            setSelectedProfiles(filteredProfiles.map((p) => p.id));
                          }
                        }}
                      />
                    </th>
                    <th className="p-2 text-left">Name</th>
                    <th className="p-2 text-left">Job Title</th>
                    <th className="p-2 text-left">Status</th>
                    <th className="p-2 text-left">Last Updated</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredProfiles.map((profile) => (
                    <tr key={profile.id} className="hover:bg-gray-50">
                      <td className="p-2">
                        <input
                          type="checkbox"
                          checked={selectedProfiles.includes(profile.id)}
                          onChange={() => {
                            setSelectedProfiles((prev) =>
                              prev.includes(profile.id)
                                ? prev.filter((id) => id !== profile.id)
                                : [...prev, profile.id]
                            );
                          }}
                        />
                      </td>
                      <td className="p-2">{profile.name}</td>
                      <td className="p-2">{profile.jobTitle}</td>
                      <td className="p-2">
                        <span className="px-2 py-1 rounded-full text-xs font-semibold bg-gray-100 text-gray-800">
                          {profile.status}
                        </span>
                      </td>
                      <td className="p-2">{profile.lastUpdated}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Assignment Section */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-4">
            <label className="font-semibold">Assign Selected Candidates to Recruiter:</label>
            <div className="w-72">
              <Input
                placeholder="Search recruiter..."
                value={targetRecruiterSearch}
                onChange={(e) => setTargetRecruiterSearch(e.target.value)}
                className="mb-2"
              />
              <Select
                value={targetRecruiter?.id || ""}
                onValueChange={(id) => {
                  const rec = recruiters.find((r) => r.id === id);
                  setTargetRecruiter(rec || null);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select recruiter" />
                </SelectTrigger>
                <SelectContent>
                  {filteredTargetRecruiters.map((r) => (
                    <SelectItem key={r.id} value={r.id}>{r.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button
              className="ml-auto"
              disabled={!selectedProfiles.length || !targetRecruiter}
              onClick={handleTransfer}
            >
              Assign Candidates
            </Button>
          </div>
        </div>
      </div>

    </div>
  );
}
