import { createSelector } from '@reduxjs/toolkit';
import type { RootState } from '../index';
import type { User } from '../slices/activeUsersSlice';

// Base selectors
export const selectActiveUsers = (state: RootState) => state.activeUsers.users;
export const selectActiveUsersLoading = (state: RootState) => state.activeUsers.loading;
export const selectActiveUsersError = (state: RootState) => state.activeUsers.error;
export const selectSearchTags = (state: RootState) => state.activeUsers.searchTags;
export const selectAppliedTags = (state: RootState) => state.activeUsers.appliedTags;
export const selectCurrentPage = (state: RootState) => state.activeUsers.currentPage;
export const selectItemsPerPage = (state: RootState) => state.activeUsers.itemsPerPage;
export const selectSortConfig = (state: RootState) => state.activeUsers.sortConfig;

// Column definitions for the table
export const columns = [
  { key: "username", label: "Username", sortable: true },
  { key: "firstName", label: "Name", sortable: true },
  { key: "email", label: "Email", sortable: true },
  { key: "userType", label: "User Type", sortable: true },
] as const;

// Suggestion priority for search
const suggestionPriority: (keyof User)[] = [
  'firstName', 'username', 'email', 'userType'
];

// Memoized selector for filtered users
export const selectFilteredUsers = createSelector(
  [selectActiveUsers, selectAppliedTags],
  (users, appliedTags) => {
    if (appliedTags.length === 0) return users;

    return users.filter(user => {
      return appliedTags.every(tag => {
        const value = user[tag.column as keyof User];
        if (typeof value === 'string') {
          return value.toLowerCase().includes(tag.value.toLowerCase());
        }
        if (typeof value === 'boolean') {
          return value.toString() === tag.value.toLowerCase();
        }
        return false;
      });
    });
  }
);

// Memoized selector for sorted users
export const selectSortedUsers = createSelector(
  [selectFilteredUsers, selectSortConfig],
  (users, sortConfig) => {
    if (!sortConfig.key || !sortConfig.direction) return users;

    const sortedUsers = [...users].sort((a, b) => {
      const aValue = a[sortConfig.key!];
      const bValue = b[sortConfig.key!];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return sortConfig.direction === 'ascending' ? comparison : -comparison;
      }

      if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
        const comparison = aValue === bValue ? 0 : aValue ? 1 : -1;
        return sortConfig.direction === 'ascending' ? comparison : -comparison;
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        const comparison = aValue - bValue;
        return sortConfig.direction === 'ascending' ? comparison : -comparison;
      }

      return 0;
    });

    return sortedUsers;
  }
);

// Memoized selector for paginated users
export const selectPaginatedUsers = createSelector(
  [selectSortedUsers, selectCurrentPage, selectItemsPerPage],
  (users, currentPage, itemsPerPage) => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return users.slice(startIndex, endIndex);
  }
);

// Memoized selector for total pages
export const selectTotalPages = createSelector(
  [selectSortedUsers, selectItemsPerPage],
  (users, itemsPerPage) => Math.ceil(users.length / itemsPerPage)
);

// Memoized selector for search suggestions
export const selectSearchSuggestions = createSelector(
  [selectActiveUsers, selectSearchTags, (state: RootState, inputValue: string) => inputValue],
  (users, searchTags, inputValue) => {
    // Only generate suggestions if there's an input value
    if (!inputValue || !inputValue.trim()) {
      return [];
    }

    const existingValues = new Set(searchTags.map(tag => `${tag.column}:${tag.value}`));
    const suggestions: { value: string; column: string }[] = [];
    const lowerInputValue = inputValue.toLowerCase();

    // Generate suggestions based on priority
    suggestionPriority.forEach(column => {
      const uniqueValues = new Set<string>();

      users.forEach(user => {
        const value = user[column];
        if (typeof value === 'string' && value.trim()) {
          // Only include values that match the input
          if (value.toLowerCase().includes(lowerInputValue)) {
            uniqueValues.add(value);
          }
        } else if (typeof value === 'boolean') {
          const boolStr = value.toString();
          if (boolStr.toLowerCase().includes(lowerInputValue)) {
            uniqueValues.add(boolStr);
          }
        }
      });

      uniqueValues.forEach(value => {
        const suggestionKey = `${column}:${value}`;
        if (!existingValues.has(suggestionKey)) {
          suggestions.push({ value, column });
        }
      });
    });

    return suggestions.slice(0, 10); // Limit to 10 suggestions
  }
);

// Memoized selector for user statistics
export const selectUserStatistics = createSelector(
  [selectActiveUsers],
  (users) => {
    const stats = {
      total: users.length,
      verified: users.filter(user => user.isVerified).length,
      active: users.filter(user => user.isActive).length,
      peerReviewers: users.filter(user => user.isPeerReviewer).length,
      management: users.filter(user => user.userType === 'management').length,
      recruiters: users.filter(user => user.userType === 'recruiter').length,
    };

    return stats;
  }
);
