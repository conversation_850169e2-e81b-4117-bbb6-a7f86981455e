import React, { useState, useEffect, useMemo, useRef } from "react";
import { Search, Plus, Trash2, Check, X } from "lucide-react";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { ApiService, type ActiveUser } from "@/services/api";

// --- TYPE DEFINITIONS ---
interface User {
  id: number;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  userType: "management" | "recruiter";
  isVerified: boolean;
  isActive: boolean;
  isPeerReviewer: boolean;
}

interface NewUserFormData {
  username: string;
  name: string;
  email: string;
  userType: "management" | "recruiter" | "";
}

interface FilterTag {
  column: string;
  value: string;
}

// --- CONSTANTS (Moved outside component for performance) ---
const columns = [
  { key: "username", label: "Username", sortable: true },
  { key: "firstName", label: "Name", sortable: true },
  { key: "email", label: "Email", sortable: true },
  { key: "userType", label: "User Type", sortable: true },
];

const suggestionPriority: (keyof User)[] = [
  'firstName', 'username', 'email', 'userType'
];


// --- HELPER FUNCTIONS ---
const convertApiUserToLocal = (apiUser: ActiveUser): User => {
  const nameParts = apiUser.name.split(' ');
  return {
    id: apiUser.id,
    username: apiUser.username,
    firstName: nameParts[0] || '',
    lastName: nameParts.slice(1).join(' ') || '',
    email: apiUser.email,
    userType: apiUser.user_type === 'management' ? 'management' : 'recruiter',
    isVerified: apiUser.is_verified,
    isActive: apiUser.is_active,
    isPeerReviewer: apiUser.peer_reviewer_status,
  };
};

// --- PARENT COMPONENT ---
export function UserAccounts() {
  return (
    <div className="h-full w-full flex flex-col">
      <div className="bg-white p-6 flex-1 w-full">
        <UserAccountsTable />
      </div>
    </div>
  );
}

// --- MAIN TABLE COMPONENT ---
function UserAccountsTable() {
  // State for API data
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // State for sorting
  const [sortConfig, setSortConfig] = useState<{ key: keyof User | null; direction: "ascending" | "descending" | null }>({ key: null, direction: null });
  
  // State for new user dialog
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newUserData, setNewUserData] = useState<NewUserFormData>({ username: "", name: "", email: "", userType: "" });
  
  // State for Advanced Search
  const [searchTags, setSearchTags] = useState<FilterTag[]>([]);
  const [appliedTags, setAppliedTags] = useState<FilterTag[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [suggestions, setSuggestions] = useState<{ value: string; column: string }[]>([]);
  const searchContainerRef = useRef<HTMLDivElement>(null);

  // Animation controls
  const { isLoading, animateSorting, animatePagination } = useTableAnimation();

  // --- DATA & UI EFFECTS ---
  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        const response = await ApiService.fetchActiveUsers();
        const allActiveUsers = [
          ...response.active_users_manager,
          ...response.active_users_recruiter,
        ];
        const convertedUsers = allActiveUsers.map(convertApiUserToLocal);
        setUsers(convertedUsers);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch users');
      } finally {
        setLoading(false);
      }
    };
    fetchUsers();
  }, []);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchContainerRef.current && !searchContainerRef.current.contains(event.target as Node)) {
        setSuggestions([]);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // --- MEMOIZED DATA PIPELINE ---
  const filteredUsers = useMemo(() => {
    const groupedTags = appliedTags.reduce((acc, tag) => {
      const key = tag.column;
      if (!acc[key]) acc[key] = [];
      acc[key].push(tag);
      return acc;
    }, {} as Record<string, FilterTag[]>);

    if (appliedTags.length === 0) {
      return users;
    }

    return users.filter((user) => {
      return Object.values(groupedTags).every(tagGroup =>
        tagGroup.some(tag => {
          const tagValue = tag.value.toLowerCase();
          const columnInfo = columns.find(c => c.label === tag.column);

          if (tag.column === 'Any') {
            return Object.values(user).some(val => String(val).toLowerCase().includes(tagValue));
          }
          if (!columnInfo) return false;
          
          if (columnInfo.key === 'firstName') {
             const fullName = `${user.firstName} ${user.lastName}`.toLowerCase();
             return fullName.includes(tagValue);
          }

          const userValue = user[columnInfo.key as keyof User];
          return String(userValue).toLowerCase().includes(tagValue);
        })
      );
    });
  }, [users, appliedTags]);

  const sortedUsers = useMemo(() => {
    return [...filteredUsers].sort((a, b) => {
      if (!sortConfig.key) return 0;
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];
      if (aValue < bValue) return sortConfig.direction === "ascending" ? -1 : 1;
      if (aValue > bValue) return sortConfig.direction === "ascending" ? 1 : -1;
      return 0;
    });
  }, [filteredUsers, sortConfig]);

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentUsers = useMemo(() => {
    return sortedUsers.slice(indexOfFirstItem, indexOfLastItem);
  }, [sortedUsers, indexOfFirstItem, indexOfLastItem]);

  // --- SUGGESTION GENERATION ---
  useEffect(() => {
    if (inputValue) {
      const newSuggestions: { value: string; column: string }[] = [];
      const addedSuggestions = new Set<string>();
      for (const key of suggestionPriority) {
        if (newSuggestions.length >= 7) break;
        const columnInfo = columns.find(c => c.key === key);
        if (!columnInfo) continue;

        for (const user of filteredUsers) {
          if (newSuggestions.length >= 7) break;
          const value = key === 'firstName' ? `${user.firstName} ${user.lastName}`.trim() : user[key];
          const suggestionKey = `${value}|${columnInfo.label}`;
          if (value && typeof value === 'string' && value.toLowerCase().includes(inputValue.toLowerCase())) {
            if (!addedSuggestions.has(suggestionKey)) {
              newSuggestions.push({ value, column: columnInfo.label });
              addedSuggestions.add(suggestionKey);
            }
          }
        }
      }
      setSuggestions(newSuggestions);
    } else {
      setSuggestions([]);
    }
  }, [inputValue, filteredUsers]);

  // --- HANDLER FUNCTIONS ---
  const handleSort = async (key: keyof User) => {
    await animateSorting();
    let direction: "ascending" | "descending" | null = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") direction = "descending";
    else if (sortConfig.key === key && sortConfig.direction === "descending") direction = null;
    setSortConfig({ key, direction });
  };
  
  const paginate = async (pageNumber: number) => {
    await animatePagination();
    setCurrentPage(pageNumber);
  };
  
  const handleAddTag = (tagOrSuggestion: string | { value: string; column: string }) => {
    const newTag: FilterTag = typeof tagOrSuggestion === 'string' ? { value: tagOrSuggestion.trim(), column: 'Any' } : { value: (tagOrSuggestion.value || '').trim(), column: tagOrSuggestion.column };
    if (newTag.value && !searchTags.some(t => t.value === newTag.value && t.column === newTag.column)) {
      setSearchTags(prev => [...prev, newTag]);
    }
    setInputValue('');
    setSuggestions([]);
  };

  const handleRemoveTag = (tagToRemove: FilterTag) => {
    setSearchTags(prev => prev.filter(tag => tag.value !== tagToRemove.value || tag.column !== tagToRemove.column));
  };

  const handleApplyFilters = () => {
    setAppliedTags([...searchTags]);
    setCurrentPage(1);
  };

  const handleClearAllFilters = () => {
    setSearchTags([]);
    setAppliedTags([]);
    setInputValue('');
    setCurrentPage(1);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };
  
  const handleAddNewUser = () => setIsDialogOpen(true);
  
  const handleSubmitNewUser = () => {
    if (!newUserData.username || !newUserData.name || !newUserData.email || !newUserData.userType) {
      alert("Please fill in all fields");
      return;
    }
    console.log("Creating new user:", newUserData);
    alert(`New user ${newUserData.name} created successfully!`);
    setNewUserData({ username: "", name: "", email: "", userType: "" });
    setIsDialogOpen(false);
  };

  const handleNewUserInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewUserData(prev => ({ ...prev, [name]: value }));
  };

  const handleUserTypeChange = (value: string) => {
    setNewUserData(prev => ({ ...prev, userType: value as "Manager" | "Recruiter" | "" }));
  };
  
  const handleToggleVerification = async (user: User, newStatus: boolean) => {
    try {
      await ApiService.updateUserStatus(user.username, newStatus, 'verify');
      setUsers(prevUsers => prevUsers.map(u => u.id === user.id ? { ...u, isVerified: newStatus } : u));
    } catch (error) {
      console.error('Failed to update verification status:', error);
      alert('Failed to update verification status');
    }
  };
  
  const handleToggleActivation = async (user: User, newStatus: boolean) => {
    try {
      await ApiService.updateUserStatus(user.username, newStatus, 'deactivate');
      setUsers(prevUsers => prevUsers.map(u => u.id === user.id ? { ...u, isActive: newStatus } : u));
    } catch (error) {
      console.error('Failed to update activation status:', error);
      alert('Failed to update activation status');
    }
  };
  
  const handleTogglePeerReviewer = async (user: User, newStatus: boolean) => {
    try {
      await ApiService.updateUserStatus(user.username, newStatus, 'peer_reviewer');
      setUsers(prevUsers => prevUsers.map(u => u.id === user.id ? { ...u, isPeerReviewer: newStatus } : u));
    } catch (error) {
      console.error('Failed to update peer reviewer status:', error);
      alert('Failed to update peer reviewer status');
    }
  };
  
  const handleDeleteUser = (userId: number) => {
    if (window.confirm(`Are you sure you want to delete user ${userId}?`)) {
        alert(`Delete user ${userId}`);
        // In a real app, you would call an API here and then filter the local state
    }
  };

  return (
    <div className="flex flex-col">
      <div className="mb-4 flex flex-wrap justify-between items-start gap-4">
        {/* Advanced Search Bar */}
        <div className="flex-1 min-w-[300px] relative" ref={searchContainerRef}>
          <div className="flex flex-wrap items-center gap-2 p-2 pr-20 border border-gray-300 rounded-md bg-white focus-within:ring-2 focus-within:ring-blue-500">
            <Search className="h-5 w-5 text-gray-400 flex-shrink-0" />
            {searchTags.map(tag => (
              <span key={`${tag.column}-${tag.value}`} className="inline-flex items-center mr-2 px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                <span className="font-normal text-blue-600 mr-1">{tag.column}:</span>
                {tag.value}
                <button onClick={() => handleRemoveTag(tag)} className="ml-1.5 -mr-1 p-0.5 rounded-full text-blue-500 hover:bg-blue-200"><X className="h-3.5 w-3.5" /></button>
              </span>
            ))}
            <input type="text" className="flex-grow min-w-[150px] h-[24px] p-1 text-sm bg-transparent outline-none" placeholder="Search users by name, email..." value={inputValue} onChange={handleInputChange} onKeyDown={(e) => { if (e.key === "Enter" && inputValue.trim()) { e.preventDefault(); handleAddTag(inputValue); } }} />
          </div>
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center">
            <button onClick={handleApplyFilters} title="Apply Filters" className="p-1.5 rounded-full hover:bg-blue-100 text-blue-600"><Check className="h-5 w-5" /></button>
            <button onClick={handleClearAllFilters} title="Clear All Filters" className="p-1.5 rounded-full hover:bg-gray-200 text-gray-500"><X className="h-5 w-5" /></button>
          </div>
          {suggestions.length > 0 && (
            <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
              {suggestions.map((s, i) => (
                <div key={`${s.value}-${i}`} className="cursor-pointer p-3 hover:bg-blue-50 flex justify-between items-center text-sm" onClick={() => handleAddTag(s)}>
                  <span className="font-medium text-gray-900">{s.value}</span>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-md font-medium">{s.column}</span>
                </div>
              ))}
            </div>
          )}
        </div>
        {/* Page Size & Add User */}
        <div className="flex items-center gap-3">
          <select className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500" onChange={async (e) => { await animatePagination(); setItemsPerPage(Number(e.target.value)); setCurrentPage(1); }} value={itemsPerPage}>
            <option value="10">10 per page</option>
            <option value="20">20 per page</option>
            <option value="50">50 per page</option>
          </select>
          <Button onClick={handleAddNewUser} className="flex items-center justify-center py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors text-sm font-medium">
            <Plus className="h-4 w-4 mr-2" />
            <span>Add New User</span>
          </Button>
        </div>
      </div>

      <AnimatedTableWrapper isLoading={isLoading || loading} className="border border-gray-200 rounded-md overflow-hidden flex-1">
        <div className="overflow-auto h-[478px]" style={{ scrollbarGutter: 'stable' }}>
          <table className="w-full table-fixed divide-y divide-gray-200" style={{ minWidth: '1200px' }}>
            <thead className="bg-gray-50 sticky top-0 z-10">
              <tr>
                {columns.map((column) => (
                  <th key={String(column.key)} scope="col" className="sticky top-0 z-10 px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider cursor-pointer bg-gray-50 border-b" onClick={() => column.sortable && handleSort(column.key)}>
                    <div className="flex items-center gap-1">{column.label}<AnimatedSortIcon direction={sortConfig.key === column.key ? sortConfig.direction : null} active={sortConfig.key === column.key} size={14}/></div>
                  </th>
                ))}
                <th className="sticky top-0 z-10 px-3 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b w-20">Verify</th>
                <th className="sticky top-0 z-10 px-3 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b w-28">Active</th>
                <th className="sticky top-0 z-10 px-3 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b w-24">Peer Reviewer</th>
                <th className="sticky top-0 z-10 px-3 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b w-20">Delete</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentUsers.length > 0 ? (
                currentUsers.map((user, index) => (
                  <AnimatedTableRow key={user.id} index={index}>
                    {columns.map((column) => (
                      <td key={`${user.id}-${String(column.key)}`} className="px-3 py-2 text-xs text-gray-800 font-medium whitespace-nowrap">
                        {column.key === "firstName" ? (`${user.firstName} ${user.lastName}`)
                          : column.key === "userType" ? (<span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-[11px] font-bold ${user.userType === "Manager" ? "bg-blue-100 text-blue-800" : "bg-green-100 text-green-800"}`}>{user.userType}</span>)
                          : (String(user[column.key]))
                        }
                      </td>
                    ))}
                    <td className="px-3 py-3 text-sm text-center"><Switch checked={user.isVerified} onCheckedChange={(c) => handleToggleVerification(user, c)} /></td>
                    <td className="px-3 py-3 text-sm text-center"><Switch checked={user.isActive} onCheckedChange={(c) => handleToggleActivation(user, c)} /></td>
                    <td className="px-3 py-3 text-sm text-center"><Switch checked={user.isPeerReviewer} onCheckedChange={(c) => handleTogglePeerReviewer(user, c)} /></td>
                    <td className="px-3 py-3 text-sm text-center"><button onClick={() => handleDeleteUser(user.id)} className="text-gray-400 hover:text-red-600"><Trash2 className="h-5 w-5 mx-auto" /></button></td>
                  </AnimatedTableRow>
                ))
              ) : (
                <tr><td colSpan={columns.length + 4} className="px-3 py-3 text-center text-sm text-gray-500">{loading ? 'Loading users...' : error ? `Error: ${error}` : 'No users found'}</td></tr>
              )}
            </tbody>
          </table>
        </div>
      </AnimatedTableWrapper>

      <div className="flex items-center justify-between mt-4 text-sm text-gray-700">
        <div>Showing {filteredUsers.length > 0 ? indexOfFirstItem + 1 : 0} to {Math.min(indexOfLastItem, filteredUsers.length)} of {filteredUsers.length} users</div>
        <AnimatedPagination currentPage={currentPage} totalPages={Math.ceil(filteredUsers.length / itemsPerPage)} onPageChange={paginate} />
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>New Account</DialogTitle>
            <DialogDescription>Create a new user account. Fill in all fields to continue.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="username" className="text-right">Username</Label>
              <Input id="username" name="username" value={newUserData.username} onChange={handleNewUserInputChange} className="col-span-3"/>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">Name</Label>
              <Input id="name" name="name" value={newUserData.name} onChange={handleNewUserInputChange} className="col-span-3"/>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">Email</Label>
              <Input id="email" name="email" type="email" value={newUserData.email} onChange={handleNewUserInputChange} className="col-span-3"/>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="userType" className="text-right">User Type</Label>
              <Select value={newUserData.userType} onValueChange={handleUserTypeChange}>
                <SelectTrigger className="col-span-3"><SelectValue placeholder="Select user type" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="Manager">Manager</SelectItem>
                  <SelectItem value="Recruiter">Recruiter</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>Cancel</Button>
            <Button type="button" onClick={handleSubmitNewUser}>Create Account</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}