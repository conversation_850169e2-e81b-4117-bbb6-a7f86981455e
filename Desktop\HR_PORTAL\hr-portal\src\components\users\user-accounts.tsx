import React, { useState, useEffect, useRef } from "react";
import { Search, Plus, Trash2, Check, X } from "lucide-react";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchActiveUsers,
  updateUserVerification,
  updateUserActiveStatus,
  updateUserPeerReviewerStatus,
  addSearchTag,
  removeSearchTag,
  applyFilters,
  clearAllFilters,
  setCurrentPage,
  setItemsPerPage,
  setSortConfig,
  type User,
  type FilterTag,
} from "@/store/slices/activeUsersSlice";
import {
  selectActiveUsers,
  selectSearchTags,
  selectActiveUsersLoading,
  selectActiveUsersError,
  selectAppliedTags,
  selectCurrentPage,
  selectItemsPerPage,
  selectSortConfig,
  selectPaginatedUsers,
  selectTotalPages,
  selectSearchSuggestions,
  columns,
} from "@/store/selectors/activeUsersSelectors";

// --- TYPE DEFINITIONS ---
interface NewUserFormData {
  username: string;
  name: string;
  email: string;
  userType: "management" | "recruiter" | "";
}



// --- PARENT COMPONENT ---
export function UserAccounts() {
  return (
    <div className="h-full w-full flex flex-col">
      <div className="bg-white p-6 flex-1 w-full">
        <UserAccountsTable />
      </div>
    </div>
  );
}

// --- MAIN TABLE COMPONENT ---
function UserAccountsTable() {
  // Redux state
  const dispatch = useAppDispatch();
  const users = useAppSelector(selectActiveUsers);
  const searchTags = useAppSelector(selectSearchTags);
  console.log('Component searchTags updated:', searchTags);
  const loading = useAppSelector(selectActiveUsersLoading);
  const error = useAppSelector(selectActiveUsersError);
  const appliedTags = useAppSelector(selectAppliedTags);
  const currentPage = useAppSelector(selectCurrentPage);
  const itemsPerPage = useAppSelector(selectItemsPerPage);
  const sortConfig = useAppSelector(selectSortConfig);
  const paginatedUsers = useAppSelector(selectPaginatedUsers);
  const totalPages = useAppSelector(selectTotalPages);
  const [inputValue, setInputValue] = useState('');
  const suggestions = useAppSelector(state => selectSearchSuggestions(state, inputValue));

  // Local state for UI only
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newUserData, setNewUserData] = useState<NewUserFormData>({ username: "", name: "", email: "", userType: "" });
  const searchContainerRef = useRef<HTMLDivElement>(null);

  // Animation controls
  const { isLoading, animateSorting, animatePagination } = useTableAnimation();

  // --- DATA & UI EFFECTS ---
  useEffect(() => {
    dispatch(fetchActiveUsers());
  }, [dispatch]);

  useEffect(() => {
    console.log('searchTags changed in useEffect:', searchTags);
  }, [searchTags]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchContainerRef.current && !searchContainerRef.current.contains(event.target as Node)) {
        setInputValue('');
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // --- HANDLER FUNCTIONS ---
  const handleSort = async (key: keyof User) => {
    await animateSorting();
    let direction: "ascending" | "descending" | null = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") direction = "descending";
    else if (sortConfig.key === key && sortConfig.direction === "descending") direction = null;
    dispatch(setSortConfig({ key, direction }));
  };

  const paginate = async (pageNumber: number) => {
    await animatePagination();
    dispatch(setCurrentPage(pageNumber));
  };

  const handleAddTag = (tagOrSuggestion: string | { value: string; column: string }) => {
    console.log('handleAddTag called with:', tagOrSuggestion);
    const newTag: FilterTag = typeof tagOrSuggestion === 'string'
      ? { value: tagOrSuggestion.trim(), column: 'Any' }
      : { value: (tagOrSuggestion.value || '').trim(), column: tagOrSuggestion.column };
    console.log('newTag created:', newTag);
    console.log('searchTags:', searchTags);
    const tagExists = searchTags.some(t => t.value === newTag.value && t.column === newTag.column);
    console.log('tagExists:', tagExists);
    if (newTag.value && !tagExists) {
      console.log('Dispatching addSearchTag');
      dispatch(addSearchTag(newTag));
    } else {
      console.log('Tag not added - either empty value or already exists');
    }
    setInputValue('');
  };

  const handleRemoveTag = (tagToRemove: FilterTag) => {
    dispatch(removeSearchTag(tagToRemove));
  };

  const handleApplyFilters = () => {
    dispatch(applyFilters());
  };

  const handleClearAllFilters = () => {
    dispatch(clearAllFilters());
    setInputValue('');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };
  
  const handleAddNewUser = () => setIsDialogOpen(true);
  
  const handleSubmitNewUser = () => {
    if (!newUserData.username || !newUserData.name || !newUserData.email || !newUserData.userType) {
      alert("Please fill in all fields");
      return;
    }
    console.log("Creating new user:", newUserData);
    alert(`New user ${newUserData.name} created successfully!`);
    setNewUserData({ username: "", name: "", email: "", userType: "" });
    setIsDialogOpen(false);
  };

  const handleNewUserInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewUserData(prev => ({ ...prev, [name]: value }));
  };

  const handleUserTypeChange = (value: string) => {
    setNewUserData(prev => ({ ...prev, userType: value as "management" | "recruiter" | "" }));
  };
  
  const handleToggleVerification = async (user: User, newStatus: boolean) => {
    try {
      await dispatch(updateUserVerification({ username: user.username, newStatus })).unwrap();
    } catch (error) {
      console.error('Failed to update verification status:', error);
      alert('Failed to update verification status');
    }
  };

  const handleToggleActivation = async (user: User, newStatus: boolean) => {
    try {
      await dispatch(updateUserActiveStatus({ username: user.username, newStatus })).unwrap();
    } catch (error) {
      console.error('Failed to update activation status:', error);
      alert('Failed to update activation status');
    }
  };

  const handleTogglePeerReviewer = async (user: User, newStatus: boolean) => {
    try {
      await dispatch(updateUserPeerReviewerStatus({ username: user.username, newStatus })).unwrap();
    } catch (error) {
      console.error('Failed to update peer reviewer status:', error);
      alert('Failed to update peer reviewer status');
    }
  };
  
  const handleDeleteUser = (userId: number) => {
    if (window.confirm(`Are you sure you want to delete user ${userId}?`)) {
        alert(`Delete user ${userId}`);
        // In a real app, you would call an API here and then filter the local state
    }
  };

  return (
    <div className="flex flex-col">
      <div className="mb-4 flex flex-wrap justify-between items-start gap-4">
        {/* Advanced Search Bar */}
        <div className="flex-1 min-w-[300px] relative" ref={searchContainerRef}>
          <div className="flex flex-wrap items-center gap-2 p-2 pr-20 border border-gray-300 rounded-md bg-white focus-within:ring-2 focus-within:ring-blue-500">
            <Search className="h-5 w-5 text-gray-400 flex-shrink-0" />
            {appliedTags.map(tag => (
              <span key={`${tag.column}-${tag.value}`} className="inline-flex items-center mr-2 px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                <span className="font-normal text-blue-600 mr-1">{tag.column}:</span>
                {tag.value}
                <button onClick={() => handleRemoveTag(tag)} className="ml-1.5 -mr-1 p-0.5 rounded-full text-blue-500 hover:bg-blue-200"><X className="h-3.5 w-3.5" /></button>
              </span>
            ))}
            <input type="text" className="flex-grow min-w-[150px] h-[24px] p-1 text-sm bg-transparent outline-none" placeholder="Search users by name, email..." value={inputValue} onChange={handleInputChange} onKeyDown={(e) => { if (e.key === "Enter" && inputValue.trim()) { e.preventDefault(); handleAddTag(inputValue); } }} />
          </div>
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center">
            <button onClick={handleApplyFilters} title="Apply Filters" className="p-1.5 rounded-full hover:bg-blue-100 text-blue-600"><Check className="h-5 w-5" /></button>
            <button onClick={handleClearAllFilters} title="Clear All Filters" className="p-1.5 rounded-full hover:bg-gray-200 text-gray-500"><X className="h-5 w-5" /></button>
          </div>
          {inputValue.trim() && suggestions.length > 0 && (
            <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
              {suggestions.map((s, i) => (
                <div key={`${s.value}-${i}`} className="cursor-pointer p-3 hover:bg-blue-50 flex justify-between items-center text-sm" onClick={() => handleAddTag(s)}>
                  <span className="font-medium text-gray-900">{s.value}</span>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-md font-medium">{s.column}</span>
                </div>
              ))}
            </div>
          )}
        </div>
        {/* Page Size & Add User */}
        <div className="flex items-center gap-3">
          <select className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500" onChange={async (e) => { await animatePagination(); setItemsPerPage(Number(e.target.value)); setCurrentPage(1); }} value={itemsPerPage}>
            <option value="10">10 per page</option>
            <option value="20">20 per page</option>
            <option value="50">50 per page</option>
          </select>
          <Button onClick={handleAddNewUser} className="flex items-center justify-center py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors text-sm font-medium">
            <Plus className="h-4 w-4 mr-2" />
            <span>Add New User</span>
          </Button>
        </div>
      </div>

      <AnimatedTableWrapper isLoading={isLoading || loading} className="border border-gray-200 rounded-md overflow-hidden flex-1">
        <div className="overflow-auto h-[478px]" style={{ scrollbarGutter: 'stable' }}>
          <table className="w-full table-fixed divide-y divide-gray-200" style={{ minWidth: '1200px' }}>
            <thead className="bg-gray-50 sticky top-0 z-10">
              <tr>
                {columns.map((column) => (
                  <th key={String(column.key)} scope="col" className="sticky top-0 z-10 px-3 py-2 text-left text-[11px] font-semibold text-gray-500 uppercase tracking-wider cursor-pointer bg-gray-50 border-b" onClick={() => column.sortable && handleSort(column.key)}>
                    <div className="flex items-center gap-1">{column.label}<AnimatedSortIcon direction={sortConfig.key === column.key ? sortConfig.direction : null} active={sortConfig.key === column.key} size={14}/></div>
                  </th>
                ))}
                <th className="sticky top-0 z-10 px-3 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b w-20">Verify</th>
                <th className="sticky top-0 z-10 px-3 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b w-28">Active</th>
                <th className="sticky top-0 z-10 px-3 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b w-24">Peer Reviewer</th>
                <th className="sticky top-0 z-10 px-3 py-2 text-center text-[11px] font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b w-20">Delete</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedUsers.length > 0 ? (
                paginatedUsers.map((user, index) => (
                  <AnimatedTableRow key={user.id} index={index}>
                    {columns.map((column) => (
                      <td key={`${user.id}-${String(column.key)}`} className="px-3 py-2 text-xs text-gray-800 font-medium whitespace-nowrap">
                        {column.key === "firstName" ? (`${user.firstName} ${user.lastName}`)
                          : column.key === "userType" ? (<span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-[11px] font-bold ${user.userType === "management" ? "bg-blue-100 text-blue-800" : "bg-green-100 text-green-800"}`}>{user.userType}</span>)
                          : (String(user[column.key]))
                        }
                      </td>
                    ))}
                    <td className="px-3 py-3 text-sm text-center"><Switch checked={user.isVerified} onCheckedChange={(c) => handleToggleVerification(user, c)} /></td>
                    <td className="px-3 py-3 text-sm text-center"><Switch checked={user.isActive} onCheckedChange={(c) => handleToggleActivation(user, c)} /></td>
                    <td className="px-3 py-3 text-sm text-center"><Switch checked={user.isPeerReviewer} onCheckedChange={(c) => handleTogglePeerReviewer(user, c)} /></td>
                    <td className="px-3 py-3 text-sm text-center"><button onClick={() => handleDeleteUser(user.id)} className="text-gray-400 hover:text-red-600"><Trash2 className="h-5 w-5 mx-auto" /></button></td>
                  </AnimatedTableRow>
                ))
              ) : (
                <tr><td colSpan={columns.length + 4} className="px-3 py-3 text-center text-sm text-gray-500">{loading ? 'Loading users...' : error ? `Error: ${error}` : 'No users found'}</td></tr>
              )}
            </tbody>
          </table>
        </div>
      </AnimatedTableWrapper>

      <div className="flex items-center justify-between mt-4 text-sm text-gray-700">
        <div>Showing {users.length > 0 ? ((currentPage - 1) * itemsPerPage) + 1 : 0} to {Math.min(currentPage * itemsPerPage, users.length)} of {users.length} users</div>
        <AnimatedPagination currentPage={currentPage} totalPages={totalPages} onPageChange={paginate} />
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>New Account</DialogTitle>
            <DialogDescription>Create a new user account. Fill in all fields to continue.</DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="username" className="text-right">Username</Label>
              <Input id="username" name="username" value={newUserData.username} onChange={handleNewUserInputChange} className="col-span-3"/>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">Name</Label>
              <Input id="name" name="name" value={newUserData.name} onChange={handleNewUserInputChange} className="col-span-3"/>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">Email</Label>
              <Input id="email" name="email" type="email" value={newUserData.email} onChange={handleNewUserInputChange} className="col-span-3"/>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="userType" className="text-right">User Type</Label>
              <Select value={newUserData.userType} onValueChange={handleUserTypeChange}>
                <SelectTrigger className="col-span-3"><SelectValue placeholder="Select user type" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="management">Manager</SelectItem>
                  <SelectItem value="recruiter">Recruiter</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>Cancel</Button>
            <Button type="button" onClick={handleSubmitNewUser}>Create Account</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}